/* Base node styles */
.react-flow__node {
  border-radius: 5px;
  font-family: 'Roboto', sans-serif;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #1976d2, 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Common node styles */
.node-content {
  padding: 8px;
  width: 180px;
}

.node-header {
  font-weight: 600;
  font-size: 14px;
  padding: 6px 8px;
  border-radius: 4px 4px 0 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-body {
  display: flex;
  padding: 8px;
  align-items: center;
}

.node-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-details {
  flex: 1;
  font-size: 12px;
}

.property-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.property-label {
  color: #666;
}

.property-value {
  font-weight: 500;
}

/* Handle styles */
.handle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #555;
}

.air-handle {
  background-color: #64b5f6; /* Light blue for air */
}

.chilled-water-handle {
  background-color: #4fc3f7; /* Cyan for chilled water */
}

.hot-water-handle {
  background-color: #ff8a65; /* Orange for hot water */
}

.condenser-water-handle {
  background-color: #81c784; /* Green for condenser water */
}

/* Specific node styles */
.air-handler-node {
  border: 2px solid #64b5f6;
  background-color: #e3f2fd;
}

.chiller-node {
  border: 2px solid #4fc3f7;
  background-color: #e1f5fe;
}

.boiler-node {
  border: 2px solid #ff8a65;
  background-color: #fff3e0;
}

.cooling-tower-node {
  border: 2px solid #81c784;
  background-color: #e8f5e9;
}

.pump-node {
  border: 2px solid #9575cd;
  background-color: #ede7f6;
}

.vav-node {
  border: 2px solid #7986cb;
  background-color: #e8eaf6;
}

.zone-node {
  border: 2px solid #a1887f;
  background-color: #efebe9;
}

.air-loop-hvac-node {
  border: 2px solid #4fc3f7;
  background-color: #e1f5fe;
}

.plant-loop-node {
  border: 2px solid #81c784;
  background-color: #e8f5e9;
}

/* Parent node styles */
.parent-node {
  min-width: 800px;
  min-height: 500px;
  padding: 20px;
  position: relative;
  z-index: 0;
  overflow: visible;
}

.parent-node .node-title {
  font-weight: bold;
  font-size: 16px;
}

.parent-node .node-subtitle {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Supply and Demand sides */
.node-side {
  position: relative;
  border: 1px dashed #ccc;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  min-height: 180px;
}

.supply-side {
  background-color: rgba(79, 195, 247, 0.1);
}

.demand-side {
  background-color: rgba(255, 138, 101, 0.1);
}

.side-header {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
}

.supply-side .side-header {
  color: #0288d1;
}

.demand-side .side-header {
  color: #e64a19;
}

.side-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.side-divider {
  height: 2px;
  background-color: #ddd;
  margin: 15px 0;
}

/* Supply and Demand buttons */
.supply-button {
  background-color: #0288d1;
}

.demand-button {
  background-color: #e64a19;
}

/* Drop zone styles */
.drop-zone-active {
  background-color: rgba(79, 195, 247, 0.2);
  border: 2px dashed #4fc3f7;
}

.drop-zone-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(79, 195, 247, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

/* Component menu styles */
.add-component-container {
  margin-top: 8px;
  position: relative;
}

.add-component-button {
  background-color: #1976d2;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  width: 100%;
  text-align: center;
  cursor: pointer;
}

.component-menu {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.component-menu-header {
  padding: 6px 8px;
  font-weight: 600;
  font-size: 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.component-menu-items {
  max-height: 200px;
  overflow-y: auto;
}

.component-menu-item {
  padding: 6px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.component-menu-item:hover {
  background-color: #f5f5f5;
}

.component-menu-item.cancel-item {
  border-top: 1px solid #e0e0e0;
  color: #f44336;
  font-weight: bold;
}
