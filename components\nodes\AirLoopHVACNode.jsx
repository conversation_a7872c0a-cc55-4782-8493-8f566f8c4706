import React, { useState } from 'react';
import { Handle, Position, useReactFlow } from 'reactflow';
import { useHVACStore } from '../../store/hvacStore';
import { airLoopHVACConfig } from './AirLoopHVACConfig';

const AirLoopHVACNode = ({ data, selected, id }) => {
  const { label, properties } = data;
  const [showComponentMenu, setShowComponentMenu] = useState(false); // false, 'supply', or 'demand'
  const [showDropZone, setShowDropZone] = useState(false);
  const { addNode, onConnect } = useHVACStore();
  const reactFlowInstance = useReactFlow();

  // Get the display properties (the ones to show in the node)
  const displayProps = airLoopHVACConfig.displayProperties || [];

  // State to track which side is being dragged over
  const [dragOverSide, setDragOverSide] = useState(null);

  // Handle drag over to show drop zone for supply side
  const handleSupplyDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('supply');
  };

  // Handle drag over to show drop zone for demand side
  const handleDemandDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    setShowDropZone(true);
    setDragOverSide('demand');
  };

  // Handle drag leave to hide drop zone
  const handleDragLeave = () => {
    setShowDropZone(false);
    setDragOverSide(null);
  };

  // Handle drop to add component inside the AirLoopHVAC
  const handleDrop = (event) => {
    event.preventDefault();
    setShowDropZone(false);

    // Get the component type from the drag data
    const componentType = event.dataTransfer.getData('application/reactflow');

    if (!componentType) {
      return;
    }

    // Check if the component is allowed on this side
    const side = dragOverSide || 'supply'; // Default to supply side if not specified

    // Get the component type with OS: prefix for checking against allowed components
    let fullComponentType;
    if (componentType === 'fanConstantVolume') {
      fullComponentType = 'OS:Fan:ConstantVolume';
    } else if (componentType === 'fanVariableVolume') {
      fullComponentType = 'OS:Fan:VariableVolume';
    } else if (componentType === 'coilCoolingWater') {
      fullComponentType = 'OS:Coil:Cooling:Water';
    } else if (componentType === 'coilHeatingWater') {
      fullComponentType = 'OS:Coil:Heating:Water';
    } else if (componentType === 'outdoorAirSystem') {
      fullComponentType = 'OS:AirLoopHVAC:OutdoorAirSystem';
    } else if (componentType === 'connectorMixer') {
      fullComponentType = 'OS:Connector:Mixer';
    } else if (componentType === 'connectorSplitter') {
      fullComponentType = 'OS:Connector:Splitter';
    } else if (componentType === 'vav') {
      fullComponentType = 'OS:AirTerminal:SingleDuct:VAV:Reheat';
    } else if (componentType === 'zone') {
      fullComponentType = 'OS:ThermalZone';
    } else if (componentType === 'zoneMixer') {
      fullComponentType = 'OS:AirLoopHVAC:ZoneMixer';
    } else if (componentType === 'zoneSplitter') {
      fullComponentType = 'OS:AirLoopHVAC:ZoneSplitter';
    } else {
      fullComponentType = `OS:${componentType}`;
    }

    const allowedOnSide = airLoopHVACConfig.sides[side].allowedComponents.includes(fullComponentType);

    if (!allowedOnSide) {
      alert(`${componentType} cannot be added to the ${side} side of the AirLoopHVAC.`);
      setDragOverSide(null);
      return;
    }

    // Calculate position for the new node inside the parent based on the side
    // Get existing nodes on this side to determine position
    const currentNodes = useHVACStore.getState().nodes;
    const existingNodes = currentNodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    let position;
    if (existingNodes.length > 0) {
      // If there are existing nodes, position the new one to the right of the rightmost node
      const rightmostNode = existingNodes.reduce((rightmost, node) => {
        return (node.position.x > rightmost.position.x) ? node : rightmost;
      }, existingNodes[0]);

      position = {
        x: rightmostNode.position.x + 150, // 150px to the right of the rightmost node
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    } else {
      // Default positions if no existing nodes
      if (side === 'supply') {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 80   // Position in the supply side
        };
      } else {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 280  // Position in the demand side
        };
      }
    }

    console.log(`Adding ${componentType} to ${side} side at position:`, position);

    // Create a new node of the selected component type with the parent ID set to this node's ID
    // Also include the side information in the node data
    const newNode = addNode(componentType, position, id, side);

    // Connect the new node based on which side it's on
    const allNodes = useHVACStore.getState().nodes;
    const sideChildNodes = allNodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    if (side === 'supply') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the supply side, connect it to the AirLoopHVAC's supply outlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'supply-outlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the supply side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    } else if (side === 'demand') {
      if (sideChildNodes.length === 1) {
        // If this is the first child on the demand side, connect it to the AirLoopHVAC's demand inlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'demand-inlet',
          targetHandle: 'inlet'
        });
      } else if (sideChildNodes.length > 1) {
        // If there are other children on the demand side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 2]; // -2 because the new node is already in the array
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    }

    // Reset the drag over side
    setDragOverSide(null);
  };

  // Handle adding a component to the AirLoopHVAC via the menu
  const handleAddComponent = (componentType, side = 'supply') => {
    // Get the current nodes to find this node's position
    const nodes = useHVACStore.getState().nodes;
    const thisNode = nodes.find(node => node.id === id);

    if (!thisNode) return;

    // Check if the component is allowed on this side
    // Get the component type with OS: prefix for checking against allowed components
    let fullComponentType;
    if (componentType === 'fanConstantVolume') {
      fullComponentType = 'OS:Fan:ConstantVolume';
    } else if (componentType === 'fanVariableVolume') {
      fullComponentType = 'OS:Fan:VariableVolume';
    } else if (componentType === 'coilCoolingWater') {
      fullComponentType = 'OS:Coil:Cooling:Water';
    } else if (componentType === 'coilHeatingWater') {
      fullComponentType = 'OS:Coil:Heating:Water';
    } else if (componentType === 'outdoorAirSystem') {
      fullComponentType = 'OS:AirLoopHVAC:OutdoorAirSystem';
    } else if (componentType === 'connectorMixer') {
      fullComponentType = 'OS:Connector:Mixer';
    } else if (componentType === 'connectorSplitter') {
      fullComponentType = 'OS:Connector:Splitter';
    } else if (componentType === 'vav') {
      fullComponentType = 'OS:AirTerminal:SingleDuct:VAV:Reheat';
    } else if (componentType === 'zone') {
      fullComponentType = 'OS:ThermalZone';
    } else if (componentType === 'zoneMixer') {
      fullComponentType = 'OS:AirLoopHVAC:ZoneMixer';
    } else if (componentType === 'zoneSplitter') {
      fullComponentType = 'OS:AirLoopHVAC:ZoneSplitter';
    } else {
      fullComponentType = `OS:${componentType}`;
    }

    const allowedOnSide = airLoopHVACConfig.sides[side].allowedComponents.includes(fullComponentType);

    if (!allowedOnSide) {
      alert(`${componentType} cannot be added to the ${side} side of the AirLoopHVAC.`);
      return;
    }

    // Calculate position for the new node inside the parent based on the side
    // Get existing nodes on this side to determine position
    const existingNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    let position;
    if (existingNodes.length > 0) {
      // If there are existing nodes, position the new one to the right of the rightmost node
      const rightmostNode = existingNodes.reduce((rightmost, node) => {
        return (node.position.x > rightmost.position.x) ? node : rightmost;
      }, existingNodes[0]);

      position = {
        x: rightmostNode.position.x + 150, // 150px to the right of the rightmost node
        y: side === 'supply' ? 80 : 280     // Y position based on side
      };
    } else {
      // Default positions if no existing nodes
      if (side === 'supply') {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 80   // Position in the supply side
        };
      } else {
        position = {
          x: 50,  // Position inside the parent (relative to parent's coordinate system)
          y: 280  // Position in the demand side
        };
      }
    }

    console.log(`Adding ${componentType} to ${side} side at position:`, position);

    // Create a new node of the selected component type with the parent ID set to this node's ID
    // Also include the side information in the node data
    const newNode = addNode(componentType, position, id, side);

    // Connect the new node based on which side it's on
    const sideChildNodes = nodes.filter(node =>
      node.parentId === id &&
      node.data.side === side
    );

    if (side === 'supply') {
      if (sideChildNodes.length === 0) {
        // If this is the first child on the supply side, connect it to the AirLoopHVAC's supply outlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'supply-outlet',
          targetHandle: 'inlet'
        });
      } else {
        // If there are other children on the supply side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 1];
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    } else if (side === 'demand') {
      if (sideChildNodes.length === 0) {
        // If this is the first child on the demand side, connect it to the AirLoopHVAC's demand inlet
        onConnect({
          source: id,
          target: newNode.id,
          sourceHandle: 'demand-inlet',
          targetHandle: 'inlet'
        });
      } else {
        // If there are other children on the demand side, connect to the last added child
        const lastChild = sideChildNodes[sideChildNodes.length - 1];
        onConnect({
          source: lastChild.id,
          target: newNode.id,
          sourceHandle: 'outlet',
          targetHandle: 'inlet'
        });
      }
    }

    // Hide the component menu
    setShowComponentMenu(false);
  };

  return (
    <div
      className={`air-loop-hvac-node parent-node ${selected ? 'selected' : ''}`}
    >
      {/* Input handles */}
      {airLoopHVACConfig.inputs.map((input, index) => (
        <Handle
          key={`input-${index}`}
          type="target"
          position={input.position}
          id={input.id}
          className={`handle ${input.className}`}
          style={input.style}
        />
      ))}

      <div className="node-content">
        <div className="node-header">
          <div className="node-title">{label}</div>
          <div className="node-subtitle">Air Loop HVAC with Supply and Demand Sides</div>
        </div>

        {/* Supply Side */}
        <div
          className={`node-side supply-side ${dragOverSide === 'supply' ? 'drop-zone-active' : ''}`}
          onDragOver={handleSupplyDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Supply Side</div>
          <div className="side-content">
            {/* Drop zone indicator for supply side */}
            {showDropZone && dragOverSide === 'supply' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Supply Side
              </div>
            )}

            {/* Add Component Button for Supply Side */}
            <div className="add-component-container">
              <button
                className="add-component-button supply-button"
                onClick={() => setShowComponentMenu('supply')}
              >
                Add to Supply Side
              </button>
            </div>
          </div>
        </div>

        {/* Divider between supply and demand sides */}
        <div className="side-divider"></div>

        {/* Demand Side */}
        <div
          className={`node-side demand-side ${dragOverSide === 'demand' ? 'drop-zone-active' : ''}`}
          onDragOver={handleDemandDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="side-header">Demand Side</div>
          <div className="side-content">
            {/* Drop zone indicator for demand side */}
            {showDropZone && dragOverSide === 'demand' && (
              <div className="drop-zone-indicator">
                Drop component here to add to Demand Side
              </div>
            )}

            {/* Add Component Button for Demand Side */}
            <div className="add-component-container">
              <button
                className="add-component-button demand-button"
                onClick={() => setShowComponentMenu('demand')}
              >
                Add to Demand Side
              </button>
            </div>
          </div>
        </div>

        {/* Component Menu */}
        {showComponentMenu && (
          <div className="component-menu">
            <div className="component-menu-header">
              Select Component for {showComponentMenu === 'supply' ? 'Supply Side' : 'Demand Side'}
            </div>
            <div className="component-menu-items">
              {showComponentMenu === 'supply' ? (
                <>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('fanConstantVolume', 'supply')}
                  >
                    Constant Volume Fan
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('fanVariableVolume', 'supply')}
                  >
                    Variable Volume Fan
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('coilCoolingWater', 'supply')}
                  >
                    Cooling Coil
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('coilHeatingWater', 'supply')}
                  >
                    Heating Coil
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('outdoorAirSystem', 'supply')}
                  >
                    Outdoor Air System
                  </div>
                </>
              ) : (
                <>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('vav', 'demand')}
                  >
                    VAV Box
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('zone', 'demand')}
                  >
                    Thermal Zone
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('zoneMixer', 'demand')}
                  >
                    Zone Mixer
                  </div>
                  <div
                    className="component-menu-item"
                    onClick={() => handleAddComponent('zoneSplitter', 'demand')}
                  >
                    Zone Splitter
                  </div>
                </>
              )}
              <div
                className="component-menu-item cancel-item"
                onClick={() => setShowComponentMenu(false)}
              >
                Cancel
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Output handles */}
      {airLoopHVACConfig.outputs.map((output, index) => (
        <Handle
          key={`output-${index}`}
          type="source"
          position={output.position}
          id={output.id}
          className={`handle ${output.className}`}
          style={output.style}
        />
      ))}
    </div>
  );
};

export default React.memo(AirLoopHVACNode);
