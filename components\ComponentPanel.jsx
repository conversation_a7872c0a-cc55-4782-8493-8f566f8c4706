import React, { useState, useCallback } from 'react';
import { useReactFlow } from 'reactflow';
import IDDComponentList from './IDDComponentList';
import { componentTypeMap } from '../services/iddService';
import { useHVACStore } from '../store/hvacStore';
import {
  airHandlerConfig,
  chillerConfig,
  boilerConfig,
  coolingTowerConfig,
  pumpConfig,
  vavConfig,
  zoneConfig
} from './nodes/nodeConfigs';
import { airLoopHVACConfig } from './nodes/AirLoopHVACConfig';
import { plantLoopConfig } from './nodes/PlantLoopConfig';
import { connectorMixerConfig, zoneMixerConfig, supplySideMixerConfig } from './nodes/MixerConfig';
import { connectorSplitterConfig, zoneSplitterConfig, supplySideSplitterConfig } from './nodes/SplitterConfig';
import { outdoorAirSystemConfig } from './nodes/OutdoorAirSystemConfig';
import { fanConstantVolumeConfig, fanVariableVolumeConfig } from './nodes/FanConfig';
import { coilCoolingWaterConfig, coilHeatingWaterConfig } from './nodes/CoilConfig';

const ComponentPanel = () => {
  const [activeTab, setActiveTab] = useState('standard');
  const reactFlowInstance = useReactFlow();
  const addNode = useHVACStore(state => state.addNode);

  const onDragStart = (event, nodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/reactflow/type', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleIDDComponentSelect = useCallback((componentType) => {
    console.log(`Selected IDD component: ${componentType}`);

    // Get the center position of the viewport
    const { x, y, zoom } = reactFlowInstance.getViewport();
    const centerX = window.innerWidth / 2 / zoom - x / zoom;
    const centerY = window.innerHeight / 2 / zoom - y / zoom;

    // Add a small random offset to avoid stacking components
    const offsetX = (Math.random() - 0.5) * 100;
    const offsetY = (Math.random() - 0.5) * 100;

    // Map OpenStudio component type to internal type if needed
    // This ensures that the component will have the correct properties
    const internalType = componentType.startsWith('OS:') ?
      (componentTypeMap[componentType] || componentType) :
      componentType;

    console.log(`Mapped component type: ${componentType} -> ${internalType}`);

    // Add the node to the canvas
    addNode(
      internalType,
      { x: centerX + offsetX, y: centerY + offsetY }
    );
  }, [reactFlowInstance, addNode]);

  // State for collapsible groups
  const [expandedGroups, setExpandedGroups] = useState({
    airSystems: true,
    plantSystems: true,
    zones: true
  });

  const toggleGroup = (groupName) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  // Component configurations organized by new structure
  const componentGroups = {
    airSystems: {
      title: 'Air Systems',
      subGroups: {
        supplySide: {
          title: 'Supply Side',
          components: [
            { type: 'airLoopHVAC', label: 'Air Loop HVAC', icon: airLoopHVACConfig.icon, className: 'air-loop-hvac-icon' },
            { type: 'outdoorAirSystem', label: 'Outdoor Air System', icon: outdoorAirSystemConfig.icon, className: 'outdoor-air-system-icon' },
            { type: 'airHandler', label: 'Air Handler', icon: airHandlerConfig.icon, className: 'air-handler-icon' },
            { type: 'fanConstantVolume', label: 'Constant Fan', icon: fanConstantVolumeConfig.icon, className: 'fan-constant-volume-icon' },
            { type: 'fanVariableVolume', label: 'Variable Fan', icon: fanVariableVolumeConfig.icon, className: 'fan-variable-volume-icon' },
            { type: 'coilCoolingWater', label: 'Cooling Coil', icon: coilCoolingWaterConfig.icon, className: 'coil-cooling-water-icon' },
            { type: 'coilHeatingWater', label: 'Heating Coil', icon: coilHeatingWaterConfig.icon, className: 'coil-heating-water-icon' },
            { type: 'supplySideMixer', label: 'Supply Mixer', icon: supplySideMixerConfig.icon, className: 'supply-mixer-icon' },
            { type: 'supplySideSplitter', label: 'Supply Splitter', icon: supplySideSplitterConfig.icon, className: 'supply-splitter-icon' }
          ]
        },
        demandSide: {
          title: 'Demand Side',
          components: [
            { type: 'vav', label: 'VAV Box', icon: vavConfig.icon, className: 'vav-icon' },
            { type: 'zoneMixer', label: 'Zone Mixer', icon: zoneMixerConfig.icon, className: 'zone-mixer-icon' },
            { type: 'zoneSplitter', label: 'Zone Splitter', icon: zoneSplitterConfig.icon, className: 'zone-splitter-icon' }
          ]
        }
      }
    },
    plantSystems: {
      title: 'Plant Systems',
      subGroups: {
        supplySide: {
          title: 'Supply Side',
          components: [
            { type: 'plantLoop', label: 'Plant Loop', icon: plantLoopConfig.icon, className: 'plant-loop-icon' },
            { type: 'chiller', label: 'Chiller', icon: chillerConfig.icon, className: 'chiller-icon' },
            { type: 'boiler', label: 'Boiler', icon: boilerConfig.icon, className: 'boiler-icon' },
            { type: 'coolingTower', label: 'Cooling Tower', icon: coolingTowerConfig.icon, className: 'cooling-tower-icon' },
            { type: 'pump', label: 'Pump', icon: pumpConfig.icon, className: 'pump-icon' },
            { type: 'connectorMixer', label: 'Water Mixer', icon: connectorMixerConfig.icon, className: 'connector-mixer-icon' },
            { type: 'connectorSplitter', label: 'Water Splitter', icon: connectorSplitterConfig.icon, className: 'connector-splitter-icon' }
          ]
        },
        demandSide: {
          title: 'Demand Side',
          components: [
            // Demand side plant components can be added here
            // For now, keeping it empty as most plant equipment is typically on supply side
          ]
        }
      }
    },
    zones: {
      title: 'Zones',
      components: [
        { type: 'zone', label: 'Zone', icon: zoneConfig.icon, className: 'zone-icon' }
      ]
    }
  };

  return (
    <div className="component-panel">
      <h3 className="panel-title">Components</h3>

      <div className="component-tabs">
        <button
          className={`tab-button ${activeTab === 'standard' ? 'active' : ''}`}
          onClick={() => setActiveTab('standard')}
        >
          Standard
        </button>
        <button
          className={`tab-button ${activeTab === 'idd' ? 'active' : ''}`}
          onClick={() => setActiveTab('idd')}
        >
          IDD Components
        </button>
      </div>

      {activeTab === 'standard' ? (
        <div className="standard-components">
          {/* Air Systems Group */}
          <div className="component-group">
            <div
              className="group-header"
              onClick={() => toggleGroup('airSystems')}
            >
              <span className={`group-toggle ${expandedGroups.airSystems ? 'expanded' : ''}`}>
                ▶
              </span>
              <h3 className="group-title">{componentGroups.airSystems.title}</h3>
            </div>
            {expandedGroups.airSystems && (
              <div className="group-content">
                {Object.entries(componentGroups.airSystems.subGroups).map(([subGroupKey, subGroup]) => (
                  <div key={subGroupKey} className="component-subgroup">
                    <h4 className="subgroup-title">{subGroup.title}</h4>
                    {subGroup.components.map((component, index) => (
                      <div
                        key={`${subGroupKey}-${index}`}
                        className="component-item"
                        draggable
                        onDragStart={(event) => onDragStart(event, component.type)}
                      >
                        <div className={`component-icon ${component.className}`}>
                          {component.icon}
                        </div>
                        <span className="component-label">{component.label}</span>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Plant Systems Group */}
          <div className="component-group">
            <div
              className="group-header"
              onClick={() => toggleGroup('plantSystems')}
            >
              <span className={`group-toggle ${expandedGroups.plantSystems ? 'expanded' : ''}`}>
                ▶
              </span>
              <h3 className="group-title">{componentGroups.plantSystems.title}</h3>
            </div>
            {expandedGroups.plantSystems && (
              <div className="group-content">
                {Object.entries(componentGroups.plantSystems.subGroups).map(([subGroupKey, subGroup]) => (
                  <div key={subGroupKey} className="component-subgroup">
                    <h4 className="subgroup-title">{subGroup.title}</h4>
                    {subGroup.components.map((component, index) => (
                      <div
                        key={`${subGroupKey}-${index}`}
                        className="component-item"
                        draggable
                        onDragStart={(event) => onDragStart(event, component.type)}
                      >
                        <div className={`component-icon ${component.className}`}>
                          {component.icon}
                        </div>
                        <span className="component-label">{component.label}</span>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Zones Group */}
          <div className="component-group">
            <div
              className="group-header"
              onClick={() => toggleGroup('zones')}
            >
              <span className={`group-toggle ${expandedGroups.zones ? 'expanded' : ''}`}>
                ▶
              </span>
              <h3 className="group-title">{componentGroups.zones.title}</h3>
            </div>
            {expandedGroups.zones && (
              <div className="group-content">
                <div className="component-category">
                  {componentGroups.zones.components.map((component, index) => (
                    <div
                      key={`zone-${index}`}
                      className="component-item"
                      draggable
                      onDragStart={(event) => onDragStart(event, component.type)}
                    >
                      <div className={`component-icon ${component.className}`}>
                        {component.icon}
                      </div>
                      <span className="component-label">{component.label}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="idd-components">
          <IDDComponentList
            onComponentSelect={handleIDDComponentSelect}
            onDragStart={onDragStart}
          />
        </div>
      )}
    </div>
  );
};

export default ComponentPanel;
